"""
retro_like_paths.py

Finds and scores every sequence (up to MAX_PLIES half‑moves)
that reaches TARGET_FEN, using
    score = |Δ eval|  – log(book_weight)
at each ply.

Requires:
    pip install python-chess stockfish

Optional:
    A Polyglot book in BIN format (e.g. 'performance.bin' from https://abrok.eu/stockfish/book/)
"""

import chess, chess.engine, chess.polyglot
import math, heapq, itertools, collections, os, sys, argparse
from typing import List, Tuple, Optional

# ----------------------------------------------------------------------
# CONFIG – edit these three paths if necessary
STOCKFISH = "stockfish"          # name or full path of the engine binary
POLYGLOT_BOOK = "performance.bin"  # or any other .bin; leave empty to disable
MAX_PLIES   = 6                    # search depth

# The demo target position: 1.d4 Nf6 2.c4 e6 3.Nf3 d5   (White to move)
TARGET_FEN  = "rnbqkb1r/ppp1pppp/4pn2/3p4/2P5/5N2/PP1P1PPP/RNBQKB1R w KQkq d6 0 4"

# ----------------------------------------------------------------------

def book_weight(book, board) -> int:
    """Return the Polyglot weight (frequency) for *board* or 0 if not found."""
    try:
        entry = book.find(board)
        return entry.weight
    except (IndexError, AttributeError):
        return 0

def cp_eval(engine, board) -> int:
    """Centipawn evaluation from the side‑to‑move’s point of view."""
    info = engine.analyse(board, chess.engine.Limit(depth=12))
    return info["score"].pov(board.turn).score(mate_score=10_000)

def forward_tree(start: chess.Board,
                 max_plies: int,
                 engine,
                 book = None):
    """
    Breadth‑first expansion up to *max_plies* plies.
    Yields tuples (board, parent_key, move_san, eval_cp, book_wt, ply_no).
    """
    Node = collections.namedtuple(
        "Node", "fen parent move eval book ply")
    root_key = start.fen()
    queue = collections.deque([root_key])
    nodes = {root_key: Node(start, None, None,
                            cp_eval(engine, start),
                            book_weight(book, start),
                            0)}
    while queue:
        key = queue.popleft()
        node = nodes[key]
        if node.ply >= max_plies:
            continue
        board = node.fen.copy()
        for move in board.legal_moves:
            child = board.copy()
            child.push(move)
            child_key = child.fen()
            if child_key in nodes:           # already visited
                continue
            nodes[child_key] = Node(child,
                                    key,
                                    board.san(move),
                                    cp_eval(engine, child),
                                    book_weight(book, child),
                                    node.ply+1)
            queue.append(child_key)
    return nodes

def extract_paths(nodes, target_fen) -> List[List[str]]:
    """Return every move‑sequence that reaches *target_fen*."""
    if target_fen not in nodes:
        return []
    paths = []
    stack = [[target_fen, []]]
    while stack:
        key, acc = stack.pop()
        node = nodes[key]
        if node.parent is None:
            paths.append(acc[::-1])        # reached root
        else:
            stack.append([node.parent, acc + [node.move]])
    return paths

def path_cost(nodes, path_fens: List[str]) -> float:
    """
    Cost = Σ |Δ eval| – log(book_wt+1) along the *positions*,
    not the moves (first element is startpos).
    """
    cost = 0.0
    for prev, nxt in zip(path_fens, path_fens[1:]):
        a, b = nodes[prev], nodes[nxt]
        cost += abs(b.eval - a.eval)           # |Δ eval|
        cost -= math.log(b.book + 1)           # frequent = cheaper
    return cost

def rebuild_fens(nodes, path_moves: List[str]) -> List[str]:
    """Convenience: starting from root, apply *path_moves* to get FEN list."""
    board = chess.Board()
    fens  = [board.fen()]
    for san in path_moves:
        board.push_san(san)
        fens.append(board.fen())
    return fens

def main():
    # -------- initialise helpers --------
    if not shutil.which(STOCKFISH):
        sys.exit(f"Cannot find '{STOCKFISH}' in PATH – edit STOCKFISH variable.")
    engine = chess.engine.SimpleEngine.popen_uci(STOCKFISH)
    book   = None
    if POLYGLOT_BOOK and os.path.exists(POLYGLOT_BOOK):
        book = chess.polyglot.open_reader(POLYGLOT_BOOK)
    start_board  = chess.Board()

    print("Building game tree ...")
    nodes = forward_tree(start_board, MAX_PLIES, engine, book)

    print(f"Found {len(nodes)} distinct positions at depth ≤ {MAX_PLIES}.")
    paths = extract_paths(nodes, TARGET_FEN)
    if not paths:
        print("TARGET_FEN unreachable within depth limit.")
        return

    scored: List[Tuple[float, List[str]]] = []
    for moves in paths:
        fens  = rebuild_fens(nodes, moves)
        cost  = path_cost(nodes, fens)
        scored.append((cost, moves))

    scored.sort(key=lambda t: t[0])

    print(f"\nTop {min(5, len(scored))} candidate line(s):")
    for rank, (c, mv) in enumerate(scored[:5], 1):
        print(f"{rank}. cost {c:.2f} : {' '.join(mv)}")

    engine.quit()
    if book: book.close()

if __name__ == "__main__":
    main()
